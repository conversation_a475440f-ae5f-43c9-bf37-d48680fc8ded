# Cattle Module Architecture

## 🏗️ **Single Core Database + Module-Specific Services**

This document explains the architecture implementation for the Cattle module, demonstrating the **Single Core Database with Module-Specific Services** pattern.

## 📁 **Directory Structure**

```
lib/Dashboard/Cattle/
├── services/
│   └── cattle_service.dart          # Module-specific service
├── models/
│   └── cattle_model.dart           # Cattle data model
├── screens/
│   ├── cattle_screen.dart          # Main cattle screen
│   └── cattle_detail_screen.dart   # Individual cattle details
├── widgets/
│   ├── cattle_list_widget.dart     # Cattle list display
│   └── cattle_card_widget.dart     # Individual cattle card
├── dialogs/
│   └── cattle_form_dialog.dart     # Add/Edit cattle form
├── tabs/
│   └── cattle_analytics_tab.dart   # Analytics dashboard
└── details/
    └── [detail tabs for individual cattle]
```

## 🔧 **Core Architecture Components**

### 1. **Single Core Database**
- **Location**: `lib/core/database/database_helper.dart`
- **Pattern**: Singleton instance
- **Purpose**: Single source of truth for all data
- **Tables**: cattle, health_records, breeding_records, weight_records, feed_records, financial_records

### 2. **Module-Specific Service**
- **Location**: `lib/Dashboard/Cattle/services/cattle_service.dart`
- **Pattern**: Singleton service accessing core database
- **Responsibilities**:
  - Cattle CRUD operations
  - Cross-module data queries
  - Cattle-specific analytics
  - Family tree operations

## 🚀 **Service Layer Implementation**

### Basic CRUD Operations
```dart
class CattleService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  Future<List<CattleModel>> getAllCattle() async {
    final db = await _databaseHelper.database;
    // Direct database access through core helper
  }
}
```

### Cross-Module Operations
The cattle service can access data from other modules through the single database:

```dart
// Health data integration
Future<List<Map<String, dynamic>>> getCattleWithHealthSummary() async {
  final db = await _databaseHelper.database;
  return await db.rawQuery('''
    SELECT c.*, COUNT(hr.id) as health_record_count
    FROM cattle c
    LEFT JOIN health_records hr ON c.id = hr.cattle_id
    GROUP BY c.id
  ''');
}

// Financial data integration
Future<Map<String, dynamic>> getCattleFinancialSummary(String cattleId) async {
  // Access financial_records table directly
}
```

## 📊 **Data Flow Architecture**

```
UI Components
    ↓
Cattle Service (Module-Specific)
    ↓
Database Helper (Core Singleton)
    ↓
SQLite Database (Single Source)
    ↓
Multiple Tables (cattle, health_records, etc.)
```

## ✅ **Benefits of This Architecture**

### 1. **Data Integrity**
- Single database ensures referential integrity
- Foreign key relationships maintained across modules
- Consistent data state across the application

### 2. **Performance**
- Single database connection pool
- Optimized cross-module queries
- Efficient joins and aggregations

### 3. **Maintainability**
- Module-specific services keep business logic organized
- Core database changes affect all modules consistently
- Easy to add new modules following the same pattern

### 4. **Scalability**
- Each module can have complex operations without affecting others
- Cross-module analytics are efficient
- Easy to add new tables and relationships

## 🔍 **Key Features Implemented**

### Cattle-Specific Operations
- ✅ Basic CRUD (Create, Read, Update, Delete)
- ✅ Search and filtering
- ✅ Status-based queries
- ✅ Breed-based queries

### Cross-Module Integration
- ✅ Health records integration
- ✅ Breeding history tracking
- ✅ Weight progression monitoring
- ✅ Financial summary calculations

### Advanced Analytics
- ✅ Comprehensive cattle statistics
- ✅ Gender and breed distribution
- ✅ Age group analysis
- ✅ Attention alerts (health, breeding, vaccinations)

### Family Tree Operations
- ✅ Parent-child relationships
- ✅ Sibling identification
- ✅ Multi-generation tracking

## 🎯 **Usage Examples**

### Basic Operations
```dart
final cattleService = CattleService();

// Get all cattle
final allCattle = await cattleService.getAllCattle();

// Search cattle
final searchResults = await cattleService.searchCattle('Holstein');

// Get cattle analytics
final analytics = await cattleService.getCattleAnalytics();
```

### Cross-Module Operations
```dart
// Get cattle with health summary
final healthSummary = await cattleService.getCattleWithHealthSummary();

// Get financial summary for specific cattle
final financials = await cattleService.getCattleFinancialSummary('cattle_001');

// Get family tree
final familyTree = await cattleService.getCattleFamilyTree('cattle_001');
```

## 🔮 **Future Module Integration**

When adding new modules (Health, Feed, Breeding, Financial), they will follow the same pattern:

```
lib/Dashboard/Health/
├── services/
│   └── health_service.dart         # Access same core database
├── models/
│   └── health_record_model.dart
└── [screens, widgets, etc.]
```

Each module service will:
1. Access the same `DatabaseHelper` singleton
2. Perform module-specific operations
3. Enable cross-module queries when needed
4. Maintain data consistency through the single database

## 📝 **Next Steps**

1. **Health Module**: Implement health_service.dart following the same pattern
2. **Feed Module**: Create feed_service.dart with feed-specific operations
3. **Breeding Module**: Add breeding_service.dart for breeding management
4. **Financial Module**: Implement financial_service.dart for cost tracking
5. **Reports Module**: Create cross-module reporting service

This architecture ensures scalability, maintainability, and data integrity as the application grows.
