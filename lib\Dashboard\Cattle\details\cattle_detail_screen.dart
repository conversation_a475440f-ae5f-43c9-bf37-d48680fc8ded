import 'package:flutter/material.dart';

class CattleDetailScreen extends StatefulWidget {
  final String cattleId;
  
  const CattleDetailScreen({
    super.key,
    required this.cattleId,
  });

  @override
  State<CattleDetailScreen> createState() => _CattleDetailScreenState();
}

class _CattleDetailScreenState extends State<CattleDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cattle Details'),
      ),
      body: Center(
        child: Text(
          'Cattle Detail Screen for ID: ${widget.cattleId}',
          style: const TextStyle(fontSize: 18),
        ),
      ),
    );
  }
}
