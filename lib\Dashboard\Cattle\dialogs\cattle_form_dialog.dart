import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/cattle_model.dart';
import '../services/cattle_service.dart';

class CattleFormDialog extends StatefulWidget {
  final String? cattleId; // null for new cattle, non-null for editing
  final VoidCallback? onSaved; // Callback when cattle is saved

  const CattleFormDialog({
    super.key,
    this.cattleId,
    this.onSaved,
  });

  @override
  State<CattleFormDialog> createState() => _CattleFormDialogState();
}

class _CattleFormDialogState extends State<CattleFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _tagController = TextEditingController();
  final _breedController = TextEditingController();
  final _weightController = TextEditingController();

  final CattleService _cattleService = CattleService();

  String _selectedGender = 'Female';
  String _selectedStatus = 'Healthy';
  DateTime _selectedBirthDate = DateTime.now().subtract(const Duration(days: 365));
  bool _isLoading = false;

  final List<String> _genders = ['Male', 'Female'];
  final List<String> _statuses = ['Healthy', 'Sick', 'Pregnant', 'Breeding'];
  final List<String> _breeds = [
    'Holstein', 'Angus', 'Jersey', 'Hereford', 'Charolais',
    'Simmental', 'Limousin', 'Brahman', 'Other'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _tagController.dispose();
    _breedController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  Future<void> _saveCattle() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final cattle = CattleModel(
        id: widget.cattleId ?? const Uuid().v4(),
        name: _nameController.text.trim(),
        tagNumber: _tagController.text.trim(),
        breed: _breedController.text.trim(),
        birthDate: _selectedBirthDate,
        gender: _selectedGender,
        weight: double.parse(_weightController.text),
        status: _selectedStatus,
        motherId: null, // TODO: Implement parent selection
        fatherId: null, // TODO: Implement parent selection
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.cattleId == null) {
        await _cattleService.createCattle(cattle);
      } else {
        await _cattleService.updateCattle(cattle);
      }

      if (mounted) {
        widget.onSaved?.call();
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.cattleId == null
                ? 'Cattle added successfully!'
                : 'Cattle updated successfully!'
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving cattle: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.cattleId == null ? 'Add New Cattle' : 'Edit Cattle'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Cattle Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter cattle name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _tagController,
                decoration: const InputDecoration(
                  labelText: 'Tag Number',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter tag number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _breedController.text.isEmpty ? _breeds.first : _breedController.text,
                decoration: const InputDecoration(
                  labelText: 'Breed',
                  border: OutlineInputBorder(),
                ),
                items: _breeds.map((breed) {
                  return DropdownMenuItem(
                    value: breed,
                    child: Text(breed),
                  );
                }).toList(),
                onChanged: (value) {
                  _breedController.text = value ?? _breeds.first;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedGender,
                      decoration: const InputDecoration(
                        labelText: 'Gender',
                        border: OutlineInputBorder(),
                      ),
                      items: _genders.map((gender) {
                        return DropdownMenuItem(
                          value: gender,
                          child: Text(gender),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedGender = value ?? 'Female';
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _weightController,
                      decoration: const InputDecoration(
                        labelText: 'Weight (kg)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Enter weight';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(),
                ),
                items: _statuses.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(status),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value ?? 'Healthy';
                  });
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCattle,
          child: _isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Text('Save'),
        ),
      ],
    );
  }
}
