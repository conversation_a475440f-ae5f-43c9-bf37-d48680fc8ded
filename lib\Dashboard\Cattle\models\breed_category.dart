class BreedCategory {
  final String id;
  final String name;
  final String animalTypeId;

  BreedCategory({
    required this.id,
    required this.name,
    required this.animalTypeId,
  });

  factory BreedCategory.fromMap(Map<String, dynamic> map) {
    return BreedCategory(
      id: map['id'] as String,
      name: map['name'] as String,
      animalTypeId: map['animal_type_id'] as String,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'animal_type_id': animalTypeId,
    };
  }
}
