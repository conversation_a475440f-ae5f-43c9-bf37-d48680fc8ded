class Cattle {
  final String id;
  final String name;
  final String tagId;
  final String gender;
  final String breedId;
  final String animalTypeId;
  final DateTime dateOfBirth;
  final String source;
  final DateTime? purchaseDate;
  final double? purchasePrice;
  final double? weight;
  final String? color;
  final String? notes;
  final String? photoPath;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String category;
  final String status;
  final String? motherTagId;

  Cattle({
    required this.id,
    required this.name,
    required this.tagId,
    required this.gender,
    required this.breedId,
    required this.animalTypeId,
    required this.dateOfBirth,
    required this.source,
    this.purchaseDate,
    this.purchasePrice,
    this.weight,
    this.color,
    this.notes,
    this.photoPath,
    required this.createdAt,
    required this.updatedAt,
    required this.category,
    required this.status,
    this.motherTagId,
  });

  factory Cattle.fromMap(Map<String, dynamic> map) {
    return Cattle(
      id: map['id'] as String,
      name: map['name'] as String,
      tagId: map['tag_id'] as String,
      gender: map['gender'] as String,
      breedId: map['breed_id'] as String,
      animalTypeId: map['animal_type_id'] as String,
      dateOfBirth: DateTime.parse(map['date_of_birth'] as String),
      source: map['source'] as String,
      purchaseDate: map['purchase_date'] != null 
          ? DateTime.parse(map['purchase_date'] as String) 
          : null,
      purchasePrice: map['purchase_price'] != null 
          ? (map['purchase_price'] as num).toDouble() 
          : null,
      weight: map['weight'] != null 
          ? (map['weight'] as num).toDouble() 
          : null,
      color: map['color'] as String?,
      notes: map['notes'] as String?,
      photoPath: map['photo_path'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      category: map['category'] as String,
      status: map['status'] as String,
      motherTagId: map['mother_tag_id'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'tag_id': tagId,
      'gender': gender,
      'breed_id': breedId,
      'animal_type_id': animalTypeId,
      'date_of_birth': dateOfBirth.toIso8601String(),
      'source': source,
      'purchase_date': purchaseDate?.toIso8601String(),
      'purchase_price': purchasePrice,
      'weight': weight,
      'color': color,
      'notes': notes,
      'photo_path': photoPath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'category': category,
      'status': status,
      'mother_tag_id': motherTagId,
    };
  }

  Cattle copyWith({
    String? id,
    String? name,
    String? tagId,
    String? gender,
    String? breedId,
    String? animalTypeId,
    DateTime? dateOfBirth,
    String? source,
    DateTime? purchaseDate,
    double? purchasePrice,
    double? weight,
    String? color,
    String? notes,
    String? photoPath,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? category,
    String? status,
    String? motherTagId,
  }) {
    return Cattle(
      id: id ?? this.id,
      name: name ?? this.name,
      tagId: tagId ?? this.tagId,
      gender: gender ?? this.gender,
      breedId: breedId ?? this.breedId,
      animalTypeId: animalTypeId ?? this.animalTypeId,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      source: source ?? this.source,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      weight: weight ?? this.weight,
      color: color ?? this.color,
      notes: notes ?? this.notes,
      photoPath: photoPath ?? this.photoPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      category: category ?? this.category,
      status: status ?? this.status,
      motherTagId: motherTagId ?? this.motherTagId,
    );
  }
}
