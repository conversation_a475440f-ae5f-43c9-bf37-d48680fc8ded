class CattleModel {
  final String id;
  final String name;
  final String tagNumber;
  final String breed;
  final DateTime birthDate;
  final String gender;
  final double weight;
  final String status;
  final String? motherId;
  final String? fatherId;
  final DateTime createdAt;
  final DateTime updatedAt;

  CattleModel({
    required this.id,
    required this.name,
    required this.tagNumber,
    required this.breed,
    required this.birthDate,
    required this.gender,
    required this.weight,
    required this.status,
    this.motherId,
    this.fatherId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CattleModel.fromJson(Map<String, dynamic> json) {
    return CattleModel(
      id: json['id'] as String,
      name: json['name'] as String,
      tagNumber: json['tag_number'] as String, // SQLite uses snake_case
      breed: json['breed'] as String,
      birthDate: DateTime.parse(json['birth_date'] as String),
      gender: json['gender'] as String,
      weight: (json['weight'] as num).toDouble(),
      status: json['status'] as String,
      motherId: json['mother_id'] as String?,
      fatherId: json['father_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'tag_number': tagNumber, // SQLite uses snake_case
      'breed': breed,
      'birth_date': birthDate.toIso8601String(),
      'gender': gender,
      'weight': weight,
      'status': status,
      'mother_id': motherId,
      'father_id': fatherId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CattleModel copyWith({
    String? id,
    String? name,
    String? tagNumber,
    String? breed,
    DateTime? birthDate,
    String? gender,
    double? weight,
    String? status,
    String? motherId,
    String? fatherId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CattleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      tagNumber: tagNumber ?? this.tagNumber,
      breed: breed ?? this.breed,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      weight: weight ?? this.weight,
      status: status ?? this.status,
      motherId: motherId ?? this.motherId,
      fatherId: fatherId ?? this.fatherId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
