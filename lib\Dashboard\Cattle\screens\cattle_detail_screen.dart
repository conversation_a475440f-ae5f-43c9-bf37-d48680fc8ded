import 'package:flutter/material.dart';
import '../details/overview_tab.dart';
import '../details/cattle_analytics_tab.dart';
import '../details/cattle_insights_tab.dart';
import '../details/family_tree_tab.dart';

class CattleDetailScreen extends StatefulWidget {
  final String cattleId;
  
  const CattleDetailScreen({
    super.key,
    required this.cattleId,
  });

  @override
  State<CattleDetailScreen> createState() => _CattleDetailScreenState();
}

class _CattleDetailScreenState extends State<CattleDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Cattle Details - ${widget.cattleId}'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Analytics'),
            Tab(text: 'Insights'),
            Tab(text: 'Family Tree'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          OverviewTab(),
          CattleAnalyticsTab(),
          CattleInsightsTab(),
          FamilyTreeTab(),
        ],
      ),
    );
  }
}
