import 'package:flutter/material.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../widgets/cattle_list_widget.dart';
import '../widgets/cattle_attention_widget.dart';

class CattleScreen extends StatefulWidget {
  const CattleScreen({super.key});

  @override
  State<CattleScreen> createState() => _CattleScreenState();
}

class _CattleScreenState extends State<CattleScreen> {
  final GlobalKey<CattleListWidgetState> _listKey = GlobalKey<CattleListWidgetState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cattle Management'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Search feature coming soon!')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // TODO: Implement filter functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Filter feature coming soon!')),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Attention widget at the top
          const CattleAttentionWidget(),
          // Main cattle list
          Expanded(
            child: CattleListWidget(key: _listKey),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => CattleFormDialog(
              onSaved: () {
                _listKey.currentState?.refreshCattle();
              },
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
