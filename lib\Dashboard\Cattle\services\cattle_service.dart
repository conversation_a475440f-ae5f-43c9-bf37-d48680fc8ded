import '../models/cattle_model.dart';

class CattleService {
  static final CattleService _instance = CattleService._internal();
  factory CattleService() => _instance;
  CattleService._internal();

  // Mock data storage - replace with actual database/API calls
  final List<CattleModel> _cattle = [];

  Future<List<CattleModel>> getAllCattle() async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    return List.from(_cattle);
  }

  Future<CattleModel?> getCattleById(String id) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 300));
    try {
      return _cattle.firstWhere((cattle) => cattle.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<CattleModel> createCattle(CattleModel cattle) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 500));
    _cattle.add(cattle);
    return cattle;
  }

  Future<CattleModel> updateCattle(CattleModel cattle) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 500));
    final index = _cattle.indexWhere((c) => c.id == cattle.id);
    if (index != -1) {
      _cattle[index] = cattle;
      return cattle;
    }
    throw Exception('Cattle not found');
  }

  Future<void> deleteCattle(String id) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 500));
    _cattle.removeWhere((cattle) => cattle.id == id);
  }

  Future<List<CattleModel>> searchCattle(String query) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 300));
    return _cattle.where((cattle) =>
        cattle.name.toLowerCase().contains(query.toLowerCase()) ||
        cattle.tagNumber.toLowerCase().contains(query.toLowerCase()) ||
        cattle.breed.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  Future<List<CattleModel>> getCattleByStatus(String status) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 300));
    return _cattle.where((cattle) => cattle.status == status).toList();
  }

  Future<List<CattleModel>> getCattleByBreed(String breed) async {
    // TODO: Implement actual API call
    await Future.delayed(const Duration(milliseconds: 300));
    return _cattle.where((cattle) => cattle.breed == breed).toList();
  }
}
