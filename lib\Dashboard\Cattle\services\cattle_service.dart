import 'package:sqflite/sqflite.dart';
import '../models/cattle_model.dart';
import '../../../core/database/database_helper.dart';

class CattleService {
  static final CattleService _instance = CattleService._internal();
  factory CattleService() => _instance;
  CattleService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<CattleModel>> getAllCattle() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('cattle');

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }

  Future<CattleModel?> getCattleById(String id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return CattleModel.from<PERSON>son(maps.first);
    }
    return null;
  }

  Future<CattleModel> createCattle(CattleModel cattle) async {
    final db = await _databaseHelper.database;
    await db.insert(
      'cattle',
      cattle.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return cattle;
  }

  Future<CattleModel> updateCattle(CattleModel cattle) async {
    final db = await _databaseHelper.database;
    await db.update(
      'cattle',
      cattle.toJson(),
      where: 'id = ?',
      whereArgs: [cattle.id],
    );
    return cattle;
  }

  Future<void> deleteCattle(String id) async {
    final db = await _databaseHelper.database;
    await db.delete(
      'cattle',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<CattleModel>> searchCattle(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'name LIKE ? OR tag_number LIKE ? OR breed LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
    );

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }

  Future<List<CattleModel>> getCattleByStatus(String status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'status = ?',
      whereArgs: [status],
    );

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }

  Future<List<CattleModel>> getCattleByBreed(String breed) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'breed = ?',
      whereArgs: [breed],
    );

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }

  // ========== CATTLE-SPECIFIC ANALYTICS ==========

  /// Get comprehensive cattle analytics
  Future<Map<String, dynamic>> getCattleAnalytics() async {
    final db = await _databaseHelper.database;

    // Basic counts
    final totalCattle = await db.rawQuery('SELECT COUNT(*) as count FROM cattle');
    final healthyCattle = await db.rawQuery('SELECT COUNT(*) as count FROM cattle WHERE status = "Healthy"');
    final sickCattle = await db.rawQuery('SELECT COUNT(*) as count FROM cattle WHERE status = "Sick"');
    final pregnantCattle = await db.rawQuery('SELECT COUNT(*) as count FROM cattle WHERE status = "Pregnant"');

    // Gender distribution
    final genderDistribution = await db.rawQuery('''
      SELECT gender, COUNT(*) as count
      FROM cattle
      GROUP BY gender
    ''');

    // Breed distribution
    final breedDistribution = await db.rawQuery('''
      SELECT breed, COUNT(*) as count
      FROM cattle
      GROUP BY breed
      ORDER BY count DESC
    ''');

    return {
      'totalCattle': (totalCattle.first['count'] as int?) ?? 0,
      'healthyCattle': (healthyCattle.first['count'] as int?) ?? 0,
      'sickCattle': (sickCattle.first['count'] as int?) ?? 0,
      'pregnantCattle': (pregnantCattle.first['count'] as int?) ?? 0,
      'genderDistribution': genderDistribution,
      'breedDistribution': breedDistribution,
    };
  }

  /// Get cattle requiring attention (health alerts, breeding schedules, etc.)
  Future<List<Map<String, dynamic>>> getCattleRequiringAttention() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT
        c.id,
        c.name,
        c.tag_number,
        c.status,
        CASE
          WHEN c.status = 'Sick' THEN 'Health Issue'
          WHEN c.status = 'Pregnant' THEN 'Pregnancy Monitoring'
          ELSE 'Routine Check'
        END as attention_reason
      FROM cattle c
      WHERE c.status IN ('Sick', 'Pregnant')
      ORDER BY
        CASE c.status
          WHEN 'Sick' THEN 1
          WHEN 'Pregnant' THEN 2
          ELSE 3
        END,
        c.name
    ''');

    return maps;
  }
}
