import 'package:sqflite/sqflite.dart';
import '../models/cattle_model.dart';
import '../../../core/database/database_helper.dart';

class CattleService {
  static final CattleService _instance = CattleService._internal();
  factory CattleService() => _instance;
  CattleService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<CattleModel>> getAllCattle() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('cattle');

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }

  Future<CattleModel?> getCattleById(String id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return CattleModel.from<PERSON>son(maps.first);
    }
    return null;
  }

  Future<CattleModel> createCattle(CattleModel cattle) async {
    final db = await _databaseHelper.database;
    await db.insert(
      'cattle',
      cattle.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return cattle;
  }

  Future<CattleModel> updateCattle(CattleModel cattle) async {
    final db = await _databaseHelper.database;
    await db.update(
      'cattle',
      cattle.toJson(),
      where: 'id = ?',
      whereArgs: [cattle.id],
    );
    return cattle;
  }

  Future<void> deleteCattle(String id) async {
    final db = await _databaseHelper.database;
    await db.delete(
      'cattle',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<CattleModel>> searchCattle(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'name LIKE ? OR tag_number LIKE ? OR breed LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
    );

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }

  Future<List<CattleModel>> getCattleByStatus(String status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'status = ?',
      whereArgs: [status],
    );

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }

  Future<List<CattleModel>> getCattleByBreed(String breed) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cattle',
      where: 'breed = ?',
      whereArgs: [breed],
    );

    return List.generate(maps.length, (i) {
      return CattleModel.fromJson(maps[i]);
    });
  }
}
