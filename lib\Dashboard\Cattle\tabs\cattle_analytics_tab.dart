import 'package:flutter/material.dart';
import '../services/cattle_service.dart';

class CattleAnalyticsTab extends StatefulWidget {
  const CattleAnalyticsTab({super.key});

  @override
  State<CattleAnalyticsTab> createState() => _CattleAnalyticsTabState();
}

class _CattleAnalyticsTabState extends State<CattleAnalyticsTab> {
  final CattleService _cattleService = CattleService();
  int _totalCattle = 0;
  int _healthyCattle = 0;
  int _sickCattle = 0;
  int _pregnantCattle = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    try {
      final allCattle = await _cattleService.getAllCattle();
      final healthy = await _cattleService.getCattleByStatus('Healthy');
      final sick = await _cattleService.getCattleByStatus('Sick');
      final pregnant = await _cattleService.getCattleByStatus('Pregnant');

      setState(() {
        _totalCattle = allCattle.length;
        _healthyCattle = healthy.length;
        _sickCattle = sick.length;
        _pregnantCattle = pregnant.length;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cattle Analytics',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildAnalyticsCard(
                  'Total Cattle',
                  _totalCattle.toString(),
                  Icons.pets,
                  Colors.blue,
                ),
                _buildAnalyticsCard(
                  'Healthy',
                  _healthyCattle.toString(),
                  Icons.favorite,
                  Colors.green,
                ),
                _buildAnalyticsCard(
                  'Sick',
                  _sickCattle.toString(),
                  Icons.medical_services,
                  Colors.red,
                ),
                _buildAnalyticsCard(
                  'Pregnant',
                  _pregnantCattle.toString(),
                  Icons.pregnant_woman,
                  Colors.purple,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 40,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
