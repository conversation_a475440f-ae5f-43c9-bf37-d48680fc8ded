import 'package:flutter/material.dart';

class CattleAnalyticsTab extends StatefulWidget {
  const CattleAnalyticsTab({super.key});

  @override
  State<CattleAnalyticsTab> createState() => _CattleAnalyticsTabState();
}

class _CattleAnalyticsTabState extends State<CattleAnalyticsTab> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cattle Analytics',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildAnalyticsCard(
                  'Total Cattle',
                  '0',
                  Icons.pets,
                  Colors.blue,
                ),
                _buildAnalyticsCard(
                  'Healthy',
                  '0',
                  Icons.favorite,
                  Colors.green,
                ),
                _buildAnalyticsCard(
                  'Under Treatment',
                  '0',
                  Icons.medical_services,
                  Colors.orange,
                ),
                _buildAnalyticsCard(
                  'Breeding',
                  '0',
                  Icons.family_restroom,
                  Colors.purple,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 40,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
