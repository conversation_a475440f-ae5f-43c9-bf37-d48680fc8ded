import 'package:flutter/material.dart';

class CattleInsightsTab extends StatefulWidget {
  const CattleInsightsTab({super.key});

  @override
  State<CattleInsightsTab> createState() => _CattleInsightsTabState();
}

class _CattleInsightsTabState extends State<CattleInsightsTab> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: <PERSON>um<PERSON>(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cattle Insights',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: <PERSON>View(
              children: [
                _buildInsightCard(
                  'Breeding Recommendations',
                  'Based on genetic analysis and breeding history',
                  Icons.lightbulb,
                  Colors.amber,
                ),
                const SizedBox(height: 16),
                _buildInsightCard(
                  'Health Alerts',
                  'Monitor cattle requiring attention',
                  Icons.warning,
                  Colors.red,
                ),
                const SizedBox(height: 16),
                _buildInsightCard(
                  'Performance Trends',
                  'Weight gain and productivity analysis',
                  Icons.trending_up,
                  Colors.green,
                ),
                const SizedBox(height: 16),
                _buildInsightCard(
                  'Feed Optimization',
                  'Recommendations for feed efficiency',
                  Icons.grass,
                  Colors.teal,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightCard(
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.1),
          child: Icon(
            icon,
            color: color,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () {
          // TODO: Navigate to detailed insight view
        },
      ),
    );
  }
}
