import 'package:flutter/material.dart';

class CattleRecordsTab extends StatefulWidget {
  const CattleRecordsTab({super.key});

  @override
  State<CattleRecordsTab> createState() => _CattleRecordsTabState();
}

class _CattleRecordsTabState extends State<CattleRecordsTab> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Cattle Records',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Add new record functionality
                },
                icon: const Icon(Icons.add),
                label: const Text('Add Record'),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView(
              children: [
                _buildRecordCard(
                  'Health Check',
                  'Routine health examination',
                  DateTime.now().subtract(const Duration(days: 2)),
                  Icons.medical_services,
                  Colors.green,
                ),
                const SizedBox(height: 12),
                _buildRecordCard(
                  'Vaccination',
                  'Annual vaccination completed',
                  DateTime.now().subtract(const Duration(days: 7)),
                  Icons.vaccines,
                  Colors.blue,
                ),
                const SizedBox(height: 12),
                _buildRecordCard(
                  'Weight Measurement',
                  'Monthly weight check - 450kg',
                  DateTime.now().subtract(const Duration(days: 14)),
                  Icons.monitor_weight,
                  Colors.orange,
                ),
                const SizedBox(height: 12),
                _buildRecordCard(
                  'Breeding Record',
                  'Artificial insemination performed',
                  DateTime.now().subtract(const Duration(days: 30)),
                  Icons.family_restroom,
                  Colors.purple,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordCard(
    String title,
    String description,
    DateTime date,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.1),
          child: Icon(
            icon,
            color: color,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            const SizedBox(height: 4),
            Text(
              '${date.day}/${date.month}/${date.year}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Text('Edit'),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Text('Delete'),
            ),
          ],
          onSelected: (value) {
            // TODO: Implement edit/delete functionality
          },
        ),
      ),
    );
  }
}
