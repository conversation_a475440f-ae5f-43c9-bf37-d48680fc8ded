import 'package:flutter/material.dart';
import '../services/cattle_service.dart';

class CattleAttentionWidget extends StatefulWidget {
  const CattleAttentionWidget({super.key});

  @override
  State<CattleAttentionWidget> createState() => _CattleAttentionWidgetState();
}

class _CattleAttentionWidgetState extends State<CattleAttentionWidget> {
  final CattleService _cattleService = CattleService();
  List<Map<String, dynamic>> _attentionList = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAttentionList();
  }

  Future<void> _loadAttentionList() async {
    try {
      final attentionList = await _cattleService.getCattleRequiringAttention();
      setState(() {
        _attentionList = attentionList;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Cattle Requiring Attention',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              )
            else if (_attentionList.isEmpty)
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: Colors.green,
                      size: 48,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'All cattle are healthy!',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _attentionList.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final cattle = _attentionList[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getStatusColor(cattle['status']),
                      child: Icon(
                        _getStatusIcon(cattle['status']),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      cattle['name'] ?? 'Unknown',
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Tag: ${cattle['tag_number'] ?? 'N/A'}'),
                        Text(
                          cattle['attention_reason'] ?? 'Needs attention',
                          style: TextStyle(
                            color: _getStatusColor(cattle['status']),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    trailing: Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                    onTap: () {
                      // Navigate to cattle detail screen
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Opening details for ${cattle['name']}'),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                  );
                },
              ),
            if (_attentionList.isNotEmpty) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _loadAttentionList,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'sick':
        return Colors.red;
      case 'pregnant':
        return Colors.blue;
      case 'healthy':
        return Colors.green;
      default:
        return Colors.orange;
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'sick':
        return Icons.local_hospital;
      case 'pregnant':
        return Icons.pregnant_woman;
      case 'healthy':
        return Icons.favorite;
      default:
        return Icons.info;
    }
  }
}
