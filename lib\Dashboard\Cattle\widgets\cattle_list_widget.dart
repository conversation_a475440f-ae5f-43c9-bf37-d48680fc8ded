import 'package:flutter/material.dart';
import '../models/cattle_model.dart';
import '../services/cattle_service.dart';
import '../screens/cattle_detail_screen.dart';

class CattleListWidget extends StatefulWidget {
  const CattleListWidget({super.key});

  @override
  State<CattleListWidget> createState() => CattleListWidgetState();
}

class CattleListWidgetState extends State<CattleListWidget> {
  final CattleService _cattleService = CattleService();
  List<CattleModel> _cattle = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCattle();
  }

  Future<void> _loadCattle() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final cattle = await _cattleService.getAllCattle();
      setState(() {
        _cattle = cattle;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading cattle: $e')),
        );
      }
    }
  }

  void refreshCattle() {
    _loadCattle();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_cattle.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.pets,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No cattle found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Tap the + button to add your first cattle',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadCattle,
      child: ListView.builder(
        itemCount: _cattle.length,
        itemBuilder: (context, index) {
          final cattle = _cattle[index];
          return _buildCattleCard(cattle);
        },
      ),
    );
  }

  Widget _buildCattleCard(CattleModel cattle) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(cattle.status).withOpacity(0.1),
          child: Text(
            cattle.name.isNotEmpty ? cattle.name[0].toUpperCase() : 'C',
            style: TextStyle(
              color: _getStatusColor(cattle.status),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          cattle.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Tag: ${cattle.tagNumber}'),
            Text('Breed: ${cattle.breed}'),
            Text('Status: ${cattle.status}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${cattle.weight.toStringAsFixed(1)} kg',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(cattle.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                cattle.status,
                style: TextStyle(
                  fontSize: 10,
                  color: _getStatusColor(cattle.status),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CattleDetailScreen(cattleId: cattle.id),
            ),
          );
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'healthy':
        return Colors.green;
      case 'sick':
        return Colors.red;
      case 'breeding':
        return Colors.purple;
      case 'pregnant':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }
}
