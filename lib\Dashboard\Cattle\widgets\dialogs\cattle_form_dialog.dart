import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/cattle.dart';
import '../models/breed_category.dart';
import '../models/animal_type.dart';
import 'package:uuid/uuid.dart';

class CattleFormDialog extends StatefulWidget {
  final Cattle? cattle;
  final List<BreedCategory> breeds;
  final List<AnimalType> animalTypes;
  final List<Cattle> existingCattle;
  final Function(Cattle) onSave;

  const CattleFormDialog({
    Key? key,
    this.cattle,
    required this.breeds,
    required this.animalTypes,
    required this.existingCattle,
    required this.onSave,
  }) : super(key: key);

  @override
  State<CattleFormDialog> createState() => _CattleFormDialogState();
}

class _CattleFormDialogState extends State<CattleFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _tagIdController = TextEditingController();
  final _weightController = TextEditingController();
  final _colorController = TextEditingController();
  final _notesController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _motherTagIdController = TextEditingController();

  String? _selectedGender;
  String? _selectedBreedId;
  String? _selectedAnimalTypeId;
  String? _selectedSource;
  String? _selectedMotherTagId;
  DateTime? _dateOfBirth;
  DateTime? _purchaseDate;
  bool _autoGenerateTagId = true;
  bool _dateOfBirthTouched = false;
  bool _purchaseDateTouched = false;

  // Store original values for editing
  String? _originalTagId;

  bool _isTagIdInUse(String tagId) {
    return widget.existingCattle.any((c) =>
        c.tagId == tagId &&
        (widget.cattle == null || c.id != widget.cattle!.id));
  }

  String _generateTagId() {
    if (_selectedAnimalTypeId == null) return '';

    final animalType = widget.animalTypes
        .firstWhere((type) => type.id == _selectedAnimalTypeId);
    final prefix = animalType.name.substring(0, 1).toUpperCase();

    // First check if the original tag ID with this prefix is available
    if (_originalTagId != null &&
        _originalTagId!.startsWith(prefix) &&
        !_isTagIdInUse(_originalTagId!)) {
      return _originalTagId!;
    }

    // Get the highest number for this prefix
    int maxNumber = 0;
    for (var cattle in widget.existingCattle) {
      if (cattle.id != widget.cattle?.id && cattle.tagId.startsWith(prefix)) {
        final numberStr = cattle.tagId.substring(1);
        final number = int.tryParse(numberStr) ?? 0;
        if (number > maxNumber) {
          maxNumber = number;
        }
      }
    }

    // Try numbers from 1 up to maxNumber to find any gaps
    for (int i = 1; i <= maxNumber + 1; i++) {
      final tagId = '$prefix$i';
      if (!_isTagIdInUse(tagId)) {
        return tagId;
      }
    }

    return '$prefix${maxNumber + 1}';
  }

  void _onAnimalTypeChanged(String? value) {
    if (value == null) return;

    setState(() {
      _selectedAnimalTypeId = value;
      _selectedBreedId = null; // Reset breed to avoid assertion error

      if (widget.cattle != null) {
        // When editing and changing animal type, always generate a new tag ID
        _tagIdController.text = '';
        _autoGenerateTagId = true;
      } else {
        // Creating new cattle
        _tagIdController.text = '';
        // Keep _autoGenerateTagId state unchanged
      }
    });
  }

  final TextStyle _headerStyle = const TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: Color(0xFF000000),
  );

  @override
  void initState() {
    super.initState();

    if (widget.cattle != null) {
      // Editing existing cattle
      _nameController.text = widget.cattle!.name;
      _tagIdController.text = widget.cattle!.tagId;
      _originalTagId = widget.cattle!.tagId;
      _selectedGender = widget.cattle!.gender;
      _selectedBreedId = widget.cattle!.breedId;
      _selectedAnimalTypeId = widget.cattle!.animalTypeId;
      _dateOfBirth = widget.cattle!.dateOfBirth;
      _selectedSource = widget.cattle!.source;
      _purchaseDate = widget.cattle!.purchaseDate;
      _purchasePriceController.text =
          widget.cattle!.purchasePrice?.toString() ?? '';
      _weightController.text = widget.cattle!.weight?.toString() ?? '';
      _colorController.text = widget.cattle!.color ?? '';
      _notesController.text = widget.cattle!.notes ?? '';
      _selectedMotherTagId = widget.cattle!.motherTagId;

      // When editing, start with auto-generate off
      _autoGenerateTagId = false;
    } else {
      // Creating new cattle
      _selectedAnimalTypeId = null;
      _selectedBreedId = null;
      _selectedGender = null;
      _selectedSource = null;
      _autoGenerateTagId = true; // Start with auto-generate on for new cattle
    }
  }

  bool _isValidTagIdFormat(String tagId) {
    if (_selectedAnimalTypeId == null) return false;

    final animalType = widget.animalTypes
        .firstWhere((type) => type.id == _selectedAnimalTypeId);
    final prefix = animalType.name.substring(0, 1).toUpperCase();

    // Format should be: prefix followed by numbers
    final RegExp tagIdRegex = RegExp('^$prefix\\d+\$');
    return tagIdRegex.hasMatch(tagId);
  }

  void _handleSave() {
    if (_formKey.currentState!.validate()) {
      try {
        // Generate new tag ID if auto-generate is on and field is empty
        if (_autoGenerateTagId && _tagIdController.text.trim().isEmpty) {
          _tagIdController.text = _generateTagId();
        }

        final cattle = _createCattleFromForm();
        widget.onSave(cattle);
        Navigator.of(context).pop();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving cattle: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Cattle _createCattleFromForm() {
    final now = DateTime.now();
    return Cattle(
      id: widget.cattle?.id ?? const Uuid().v4(), // Use UUID as default ID
      name: _nameController.text.trim(),
      tagId: _tagIdController.text.trim(),
      gender: _selectedGender!,
      breedId: _selectedBreedId!,
      animalTypeId: _selectedAnimalTypeId!,
      dateOfBirth: _dateOfBirth!,
      source: _selectedSource!,
      purchaseDate: _purchaseDate,
      purchasePrice: _purchasePriceController.text.isNotEmpty
          ? double.tryParse(_purchasePriceController.text.trim())
          : null,
      weight: _weightController.text.isNotEmpty
          ? double.tryParse(_weightController.text.trim())
          : null,
      color: _colorController.text.trim(),
      notes: _notesController.text.trim(),
      photoPath: null,
      createdAt: widget.cattle?.createdAt ?? now,
      updatedAt: now,
      category: 'Unknown',
      status: 'Active',
    );
  }

  String _getAnimalTypePrefix() {
    if (_selectedAnimalTypeId == null) return 'X';
    return widget.animalTypes
        .firstWhere((type) => type.id == _selectedAnimalTypeId)
        .name
        .substring(0, 1)
        .toUpperCase();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _tagIdController.dispose();
    _weightController.dispose();
    _colorController.dispose();
    _notesController.dispose();
    _purchasePriceController.dispose();
    _motherTagIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFFF5F5F1),
      insetPadding:
          const EdgeInsets.symmetric(horizontal: 24.0, vertical: 24.0),
      content: SizedBox(
        width: 300,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: Text(
                    widget.cattle == null ? 'Add Cattle' : 'Edit Cattle',
                    style: _headerStyle,
                  ),
                ),
                const SizedBox(height: 24),

                // Animal Type Dropdown
                DropdownButtonFormField<String>(
                  value: _selectedAnimalTypeId,
                  decoration: const InputDecoration(
                    labelText: 'Animal Type',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      widget.animalTypes.map<DropdownMenuItem<String>>((type) {
                    return DropdownMenuItem(
                      value: type.id,
                      child: Text(type.name),
                    );
                  }).toList(),
                  onChanged: _onAnimalTypeChanged,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select an animal type';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Breed Dropdown (filtered by animal type)
                if (_selectedAnimalTypeId != null)
                  DropdownButtonFormField<String>(
                    value: _selectedBreedId,
                    decoration: const InputDecoration(
                      labelText: 'Breed',
                      border: OutlineInputBorder(),
                    ),
                    items: widget.breeds
                        .where((breed) =>
                            breed.animalTypeId == _selectedAnimalTypeId)
                        .map<DropdownMenuItem<String>>((breed) {
                      return DropdownMenuItem(
                        value: breed.id,
                        child: Text(breed.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedBreedId = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a breed';
                      }
                      return null;
                    },
                  ),
                if (_selectedAnimalTypeId != null) const SizedBox(height: 16),

                // Name Field
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Tag ID Field with Auto-generate Toggle
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 60,
                        child: TextFormField(
                          controller: _tagIdController,
                          enabled: !_autoGenerateTagId,
                          decoration: InputDecoration(
                            labelText: 'Tag ID',
                            border: const OutlineInputBorder(),
                            helperText: !_autoGenerateTagId
                                ? 'Format: ${_getAnimalTypePrefix()}N (N = number)'
                                : null,
                          ),
                          validator: (value) {
                            if (!_autoGenerateTagId) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter a tag ID';
                              }
                              if (!_isValidTagIdFormat(value)) {
                                return 'Invalid tag ID format';
                              }
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text('Auto', style: TextStyle(fontSize: 14)),
                        Switch(
                          value: _autoGenerateTagId,
                          activeColor: Colors.white,
                          activeTrackColor: const Color(0xFF2E7D32),
                          inactiveTrackColor: Colors.grey.shade300,
                          inactiveThumbColor: Colors.grey.shade50,
                          onChanged: (value) {
                            setState(() {
                              _autoGenerateTagId = value;
                              if (value) {
                                _tagIdController.text = '';
                              }
                            });
                          },
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Gender Field
                DropdownButtonFormField<String>(
                  value: _selectedGender,
                  decoration: const InputDecoration(
                    labelText: 'Gender',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'Male', child: Text('Male')),
                    DropdownMenuItem(value: 'Female', child: Text('Female')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a gender';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Source Field
                DropdownButtonFormField<String>(
                  value: _selectedSource,
                  decoration: const InputDecoration(
                    labelText: 'Source',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                        value: 'Born at Farm', child: Text('Born at Farm')),
                    DropdownMenuItem(
                        value: 'Purchased', child: Text('Purchased')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedSource = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a source';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Source specific fields
                if (_selectedSource == 'Purchased') ...[
                  // Purchase Date
                  InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _purchaseDate ?? DateTime.now(),
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() {
                          _purchaseDate = date;
                          _purchaseDateTouched = true;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Purchase Date',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                        errorText: _purchaseDateTouched && _purchaseDate == null
                            ? 'Please select purchase date'
                            : null,
                      ),
                      child: Text(
                        _purchaseDate != null
                            ? DateFormat('MMM dd, yyyy').format(_purchaseDate!)
                            : 'Select Date',
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _purchasePriceController,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    decoration: const InputDecoration(
                      labelText: 'Purchase Price',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                    ),
                    validator: (value) {
                      if (_selectedSource == 'Purchased') {
                        if (value == null || value.isEmpty) {
                          return 'Please enter purchase price';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                      }
                      return null;
                    },
                  ),
                ] else if (_selectedSource == 'Born at Farm') ...[
                  // Date of Birth
                  InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _dateOfBirth ?? DateTime.now(),
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() {
                          _dateOfBirth = date;
                          _dateOfBirthTouched = true;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Date of Birth',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                        errorText: _dateOfBirthTouched && _dateOfBirth == null
                            ? 'Please select date of birth'
                            : null,
                      ),
                      child: Text(
                        _dateOfBirth != null
                            ? DateFormat('MMM dd, yyyy').format(_dateOfBirth!)
                            : 'Select Date',
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Mother Tag ID
                  if (_selectedAnimalTypeId != null)
                    DropdownButtonFormField<String>(
                      value: _selectedMotherTagId,
                      decoration: const InputDecoration(
                        labelText: 'Mother Tag ID',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      ),
                      hint: const Text('Select Mother'),
                      items: widget.existingCattle
                          .where((c) =>
                              c.gender == 'Female' &&
                              c.animalTypeId == _selectedAnimalTypeId)
                          .map<DropdownMenuItem<String>>(
                              (c) => DropdownMenuItem(
                                    value: c.tagId,
                                    child: Text('${c.tagId} - ${c.name}'),
                                  ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedMotherTagId = value;
                        });
                      },
                      validator: (value) {
                        if (_selectedSource == 'Born at Farm' &&
                            (value == null || value.isEmpty)) {
                          return 'Please select mother tag ID';
                        }
                        return null;
                      },
                    ),
                ],

                const SizedBox(height: 24),
                const Divider(height: 1, thickness: 1),
                const SizedBox(height: 24),
                // Optional Information section
                Text(
                  'Optional Information',
                  style: _headerStyle,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _weightController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  decoration: const InputDecoration(
                    labelText: 'Weight (kg)',
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _colorController,
                  decoration: const InputDecoration(
                    labelText: 'Color',
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            foregroundColor: Theme.of(context).primaryColor,
          ),
          child: const Text('CANCEL'),
        ),
        ElevatedButton(
          onPressed: _handleSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2E7D32),
            foregroundColor: Colors.white,
          ),
          child: Text(widget.cattle == null ? 'ADD CATTLE' : 'SAVE CHANGES'),
        ),
      ],
    );
  }
}
