import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'cattle_manager.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create cattle table
    await db.execute('''
      CREATE TABLE cattle (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        tag_number TEXT UNIQUE NOT NULL,
        breed TEXT NOT NULL,
        birth_date TEXT NOT NULL,
        gender TEXT NOT NULL,
        weight REAL NOT NULL,
        status TEXT NOT NULL,
        mother_id TEXT,
        father_id TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (mother_id) REFERENCES cattle (id),
        FOREIGN KEY (father_id) REFERENCES cattle (id)
      )
    ''');

    // Create health_records table
    await db.execute('''
      CREATE TABLE health_records (
        id TEXT PRIMARY KEY,
        cattle_id TEXT NOT NULL,
        record_type TEXT NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        veterinarian TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (cattle_id) REFERENCES cattle (id) ON DELETE CASCADE
      )
    ''');

    // Create breeding_records table
    await db.execute('''
      CREATE TABLE breeding_records (
        id TEXT PRIMARY KEY,
        mother_id TEXT NOT NULL,
        father_id TEXT,
        breeding_date TEXT NOT NULL,
        breeding_method TEXT NOT NULL,
        expected_due_date TEXT,
        actual_birth_date TEXT,
        offspring_id TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (mother_id) REFERENCES cattle (id) ON DELETE CASCADE,
        FOREIGN KEY (father_id) REFERENCES cattle (id),
        FOREIGN KEY (offspring_id) REFERENCES cattle (id)
      )
    ''');

    // Create weight_records table
    await db.execute('''
      CREATE TABLE weight_records (
        id TEXT PRIMARY KEY,
        cattle_id TEXT NOT NULL,
        weight REAL NOT NULL,
        date TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (cattle_id) REFERENCES cattle (id) ON DELETE CASCADE
      )
    ''');

    // Insert sample data
    await _insertSampleData(db);
  }

  Future<void> _insertSampleData(Database db) async {
    // Sample cattle data
    await db.insert('cattle', {
      'id': 'cattle_001',
      'name': 'Bella',
      'tag_number': 'C001',
      'breed': 'Holstein',
      'birth_date': '2022-03-15',
      'gender': 'Female',
      'weight': 450.5,
      'status': 'Healthy',
      'mother_id': null,
      'father_id': null,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    await db.insert('cattle', {
      'id': 'cattle_002',
      'name': 'Thunder',
      'tag_number': 'C002',
      'breed': 'Angus',
      'birth_date': '2021-08-20',
      'gender': 'Male',
      'weight': 680.0,
      'status': 'Healthy',
      'mother_id': null,
      'father_id': null,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    await db.insert('cattle', {
      'id': 'cattle_003',
      'name': 'Daisy',
      'tag_number': 'C003',
      'breed': 'Jersey',
      'birth_date': '2023-01-10',
      'gender': 'Female',
      'weight': 380.0,
      'status': 'Pregnant',
      'mother_id': 'cattle_001',
      'father_id': 'cattle_002',
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    // Sample health records
    await db.insert('health_records', {
      'id': 'health_001',
      'cattle_id': 'cattle_001',
      'record_type': 'Vaccination',
      'description': 'Annual vaccination completed',
      'date': '2024-01-15',
      'veterinarian': 'Dr. Smith',
      'notes': 'No adverse reactions observed',
      'created_at': DateTime.now().toIso8601String(),
    });

    await db.insert('health_records', {
      'id': 'health_002',
      'cattle_id': 'cattle_002',
      'record_type': 'Health Check',
      'description': 'Routine health examination',
      'date': '2024-02-20',
      'veterinarian': 'Dr. Johnson',
      'notes': 'All vitals normal',
      'created_at': DateTime.now().toIso8601String(),
    });

    // Sample weight records
    await db.insert('weight_records', {
      'id': 'weight_001',
      'cattle_id': 'cattle_001',
      'weight': 450.5,
      'date': '2024-01-01',
      'notes': 'Monthly weight check',
      'created_at': DateTime.now().toIso8601String(),
    });

    await db.insert('weight_records', {
      'id': 'weight_002',
      'cattle_id': 'cattle_002',
      'weight': 680.0,
      'date': '2024-01-01',
      'notes': 'Monthly weight check',
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
